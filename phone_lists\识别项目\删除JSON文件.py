#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
选择性删除JSON文件和/或对应的JPG文件（同名文件对）
支持三种删除模式：
1. 只删除JSON文件（保留图片）
2. 只删除图片文件（保留JSON）
3. 删除JSON和图片文件（删除文件对）
"""

from pathlib import Path

def find_and_delete_paired_files(directory, delete_mode="both"):
    """
    查找并删除JSON文件和/或对应的JPG文件（同名文件对）
    :param directory: 要搜索的目录
    :param delete_mode: 删除模式 - "json"只删除JSON, "image"只删除图片, "both"删除两者
    """
    directory = Path(directory)

    if not directory.exists():
        print(f"❌ 目录不存在: {directory}")
        return

    print(f"🔍 搜索目录: {directory}")
    print("=" * 60)

    # 统计变量
    total_json_files = 0
    deleted_json_files = 0
    deleted_image_files = 0
    total_image_files = 0

    # 递归搜索所有文件
    for file_path in directory.rglob('*'):
        if file_path.is_file():
            if file_path.suffix.lower() == '.json':
                total_json_files += 1

                # 检查是否存在同名的JPG文件
                jpg_path = file_path.with_suffix('.jpg')
                JPG_path = file_path.with_suffix('.JPG')
                jpeg_path = file_path.with_suffix('.jpeg')
                JPEG_path = file_path.with_suffix('.JPEG')

                # 检查各种可能的图片文件扩展名
                image_paths = [jpg_path, JPG_path, jpeg_path, JPEG_path]
                existing_image = None

                for img_path in image_paths:
                    if img_path.exists():
                        existing_image = img_path
                        break

                if existing_image:
                    # 根据删除模式显示不同的信息
                    if delete_mode == "json":
                        print(f"🗑️  删除JSON文件:")
                        print(f"   JSON: {file_path.name}")
                        print(f"   保留图片: {existing_image.name}")
                    elif delete_mode == "image":
                        print(f"🗑️  删除图片文件:")
                        print(f"   保留JSON: {file_path.name}")
                        print(f"   图片: {existing_image.name}")
                    else:  # both
                        print(f"🗑️  删除文件对:")
                        print(f"   JSON: {file_path.name}")
                        print(f"   图片: {existing_image.name}")
                    print(f"   路径: {file_path.parent}")

                    # 根据删除模式删除文件
                    if delete_mode in ["json", "both"]:
                        # 删除JSON文件
                        try:
                            file_path.unlink()
                            deleted_json_files += 1
                            print("   ✅ JSON文件删除成功")
                        except Exception as e:
                            print(f"   ❌ JSON文件删除失败: {e}")

                    if delete_mode in ["image", "both"]:
                        # 删除对应的图片文件
                        try:
                            existing_image.unlink()
                            deleted_image_files += 1
                            print("   ✅ 图片文件删除成功")
                        except Exception as e:
                            print(f"   ❌ 图片文件删除失败: {e}")

                    print()

            elif file_path.suffix.lower() in ['.jpg', '.jpeg']:
                total_image_files += 1

    print("=" * 60)
    print("📊 统计结果:")
    print(f"   - 总图片文件数: {total_image_files} 个")
    print(f"   - 总JSON文件数: {total_json_files} 个")
    print(f"   - 删除的JSON文件: {deleted_json_files} 个")
    print(f"   - 删除的图片文件: {deleted_image_files} 个")
    print(f"   - 删除的文件对: {min(deleted_json_files, deleted_image_files)} 对")
    print(f"   - 剩余JSON文件: {total_json_files - deleted_json_files} 个")
    print(f"   - 剩余图片文件: {total_image_files - deleted_image_files} 个")

def preview_files_to_delete(directory, delete_mode="both"):
    """
    预览将要删除的文件对，不实际删除
    :param directory: 要搜索的目录
    :param delete_mode: 删除模式 - "json"只删除JSON, "image"只删除图片, "both"删除两者
    """
    directory = Path(directory)

    if not directory.exists():
        print(f"❌ 目录不存在: {directory}")
        return

    print(f"🔍 预览模式 - 搜索目录: {directory}")
    print("=" * 60)

    # 统计变量
    total_json_files = 0
    total_image_files = 0
    to_delete_pairs = 0

    # 递归搜索所有文件
    for file_path in directory.rglob('*'):
        if file_path.is_file():
            if file_path.suffix.lower() == '.json':
                total_json_files += 1

                # 检查是否存在同名的JPG文件
                jpg_path = file_path.with_suffix('.jpg')
                JPG_path = file_path.with_suffix('.JPG')
                jpeg_path = file_path.with_suffix('.jpeg')
                JPEG_path = file_path.with_suffix('.JPEG')

                # 检查各种可能的图片文件扩展名
                image_paths = [jpg_path, JPG_path, jpeg_path, JPEG_path]
                existing_image = None

                for img_path in image_paths:
                    if img_path.exists():
                        existing_image = img_path
                        break

                if existing_image:
                    # 根据删除模式显示不同的预览信息
                    if delete_mode == "json":
                        print(f"📄 将删除JSON文件 #{to_delete_pairs + 1}:")
                        print(f"   JSON: {file_path.name}")
                        print(f"   保留图片: {existing_image.name}")
                    elif delete_mode == "image":
                        print(f"📄 将删除图片文件 #{to_delete_pairs + 1}:")
                        print(f"   保留JSON: {file_path.name}")
                        print(f"   图片: {existing_image.name}")
                    else:  # both
                        print(f"📄 将删除文件对 #{to_delete_pairs + 1}:")
                        print(f"   JSON: {file_path.name}")
                        print(f"   图片: {existing_image.name}")
                    print(f"   路径: {file_path.parent}")
                    print()

                    to_delete_pairs += 1

            elif file_path.suffix.lower() in ['.jpg', '.jpeg']:
                total_image_files += 1

    print("=" * 60)
    print("📊 预览结果:")
    print(f"   - 总图片文件数: {total_image_files} 个")
    print(f"   - 总JSON文件数: {total_json_files} 个")
    print(f"   - 找到的文件对: {to_delete_pairs} 对")

    if delete_mode == "json":
        print(f"   - 将要删除的JSON文件: {to_delete_pairs} 个")
        print(f"   - 将保留的JSON文件: {total_json_files - to_delete_pairs} 个")
        print(f"   - 将保留的图片文件: {total_image_files} 个")
    elif delete_mode == "image":
        print(f"   - 将要删除的图片文件: {to_delete_pairs} 个")
        print(f"   - 将保留的JSON文件: {total_json_files} 个")
        print(f"   - 将保留的图片文件: {total_image_files - to_delete_pairs} 个")
    else:  # both
        print(f"   - 将要删除的JSON文件: {to_delete_pairs} 个")
        print(f"   - 将要删除的图片文件: {to_delete_pairs} 个")
        print(f"   - 将保留的JSON文件: {total_json_files - to_delete_pairs} 个")
        print(f"   - 将保留的图片文件: {total_image_files - to_delete_pairs} 个")

def get_delete_mode():
    """获取删除模式"""
    print("\n请选择删除模式:")
    print("1. 只删除JSON文件（保留图片）")
    print("2. 只删除图片文件（保留JSON）")
    print("3. 删除JSON和图片文件（删除文件对）")

    while True:
        choice = input("\n请输入选择 (1/2/3): ").strip()

        if choice == "1":
            return "json"
        elif choice == "2":
            return "image"
        elif choice == "3":
            return "both"
        else:
            print("❌ 无效选择，请重新输入")

def main():
    """主函数"""

    print("🗑️  文件对清理工具")
    print("功能：选择性删除JSON文件和/或对应的JPG文件（同名文件对）")
    print()
    
    # 获取要处理的目录
    default_dir = input("请输入要处理的目录路径（默认为当前目录）: ").strip()
    if not default_dir:
        default_dir = "."
    
    directory = Path(default_dir)
    
    if not directory.exists():
        print(f"❌ 目录不存在: {directory}")
        return
    
    print(f"\n📁 处理目录: {directory.absolute()}")

    # 获取删除模式
    delete_mode = get_delete_mode()

    # 显示当前删除模式
    mode_desc = {
        "json": "只删除JSON文件（保留图片）",
        "image": "只删除图片文件（保留JSON）",
        "both": "删除JSON和图片文件（删除文件对）"
    }
    print(f"\n✅ 当前删除模式: {mode_desc[delete_mode]}")

    while True:
        print("\n请选择操作:")
        print("1. 预览将要删除的文件（不实际删除）")
        print("2. 执行删除操作")
        print("3. 更改删除模式")
        print("4. 退出")

        choice = input("\n请输入选择 (1/2/3/4): ").strip()

        if choice == "1":
            print("\n🔍 预览模式...")
            preview_files_to_delete(directory, delete_mode)

        elif choice == "2":
            print(f"\n⚠️  警告：此操作将永久删除文件！")
            print(f"删除模式: {mode_desc[delete_mode]}")
            confirm = input("确认要执行删除操作吗？(输入 'yes' 确认): ").strip().lower()

            if confirm == 'yes':
                print("\n🗑️  开始删除操作...")
                find_and_delete_paired_files(directory, delete_mode)
            else:
                print("❌ 操作已取消")

        elif choice == "3":
            delete_mode = get_delete_mode()
            print(f"\n✅ 删除模式已更改为: {mode_desc[delete_mode]}")

        elif choice == "4":
            print("👋 程序退出")
            break

        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
