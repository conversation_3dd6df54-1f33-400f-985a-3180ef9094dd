#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的身份证正反面识别系统
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入整理脚本的类
from 整理 import IDCardOrganizer

def test_keyword_scoring():
    """测试关键词评分系统"""
    
    print("🧪 测试改进后的身份证识别系统")
    print("="*60)
    
    # 创建测试用的识别器实例
    organizer = IDCardOrganizer("", "./test_output", "both", (400, 250))
    
    # 测试用例：模拟OCR识别结果
    test_cases = [
        {
            "name": "身份证正面测试1",
            "ocr_results": [
                {"text": "中华人民共和国"},
                {"text": "居民身份证"},
                {"text": "姓名"},
                {"text": "张三"},
                {"text": "性别"},
                {"text": "男"},
                {"text": "民族"},
                {"text": "汉族"},
                {"text": "出生"},
                {"text": "1990年01月01日"},
                {"text": "住址"},
                {"text": "北京市朝阳区"},
                {"text": "公民身份号码"},
                {"text": "110101199001011234"}
            ],
            "expected": "front"
        },
        {
            "name": "身份证正面测试2（简化版）",
            "ocr_results": [
                {"text": "姓名"},
                {"text": "李四"},
                {"text": "性别"},
                {"text": "女"},
                {"text": "公民身份号码"},
                {"text": "110101199002022345"}
            ],
            "expected": "front"
        },
        {
            "name": "身份证反面测试1",
            "ocr_results": [
                {"text": "签发机关"},
                {"text": "北京市公安局朝阳分局"},
                {"text": "有效期限"},
                {"text": "2020.01.01-2030.01.01"},
                {"text": "签发日期"},
                {"text": "2020年01月01日"}
            ],
            "expected": "back"
        },
        {
            "name": "身份证反面测试2（长期有效）",
            "ocr_results": [
                {"text": "签发机关"},
                {"text": "上海市公安局"},
                {"text": "有效期限"},
                {"text": "2020.01.01-长期"},
                {"text": "派出所"}
            ],
            "expected": "back"
        },
        {
            "name": "身份证反面测试3（简化版）",
            "ocr_results": [
                {"text": "公安局"},
                {"text": "有效期"},
                {"text": "长期"},
                {"text": "2020.01.01"}
            ],
            "expected": "back"
        },
        {
            "name": "混合内容测试（分数不足）",
            "ocr_results": [
                {"text": "姓名"},
                {"text": "王五"},
                {"text": "公安"},
                {"text": "局"}
            ],
            "expected": "unknown"  # 分数都不足，应该判断为unknown
        },
        {
            "name": "无关内容测试",
            "ocr_results": [
                {"text": "这是一些"},
                {"text": "无关的文字"},
                {"text": "测试内容"}
            ],
            "expected": "unknown"
        }
    ]
    
    # 执行测试
    correct_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print("-" * 40)
        
        # 显示模拟的OCR结果
        ocr_texts = [result['text'] for result in test_case['ocr_results']]
        print(f"模拟OCR结果: {', '.join(ocr_texts)}")
        
        # 执行识别
        result = organizer.identify_id_card_side(test_case['ocr_results'])
        expected = test_case['expected']
        
        # 判断结果
        is_correct = result == expected
        status = "✅ 正确" if is_correct else "❌ 错误"
        
        print(f"识别结果: {result}")
        print(f"期望结果: {expected}")
        print(f"测试状态: {status}")
        
        if is_correct:
            correct_count += 1
    
    # 显示总体测试结果
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    print(f"总测试用例: {total_count}")
    print(f"正确识别: {correct_count}")
    print(f"错误识别: {total_count - correct_count}")
    print(f"准确率: {correct_count/total_count*100:.1f}%")
    
    if correct_count == total_count:
        print("🎉 所有测试用例都通过了！")
    else:
        print("⚠️  有部分测试用例未通过，可能需要进一步调整识别逻辑。")

def test_keyword_categories():
    """测试关键词分类"""
    
    print("\n" + "="*60)
    print("🔍 关键词分类测试")
    print("="*60)
    
    # 创建测试用的识别器实例
    organizer = IDCardOrganizer("", "./test_output", "both", (400, 250))
    
    print("📝 正面关键词分类:")
    print(f"重要关键词 (+3分): {organizer.front_keywords_high}")
    print(f"一般关键词 (+2分): {organizer.front_keywords_medium}")
    print(f"其他关键词 (+1分): {organizer.front_keywords_low}")
    
    print("\n📝 反面关键词分类:")
    print(f"重要关键词 (+3分): {organizer.back_keywords_high}")
    print(f"一般关键词 (+2分): {organizer.back_keywords_medium}")
    print(f"其他关键词 (+1分): {organizer.back_keywords_low}")

if __name__ == "__main__":
    try:
        # 测试关键词分类
        test_keyword_categories()
        
        # 测试识别系统
        test_keyword_scoring()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保CnOcr库已安装: pip install cnocr")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
