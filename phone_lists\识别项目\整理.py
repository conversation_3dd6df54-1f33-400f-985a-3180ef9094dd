import os
import sys
import shutil
from pathlib import Path
import cv2
import json
from datetime import datetime
import numpy as np

print("开始身份证OCR识别...")

try:
    from cnocr import CnOcr
    print("CnOcr库导入成功")
except ImportError as e:
    print(f"CnOcr库导入失败: {e}")
    print("请先安装CnOcr库: pip install cnocr")
    sys.exit(1)

class IDCardOrganizer:
    def __init__(self, source_dir, output_dir, recognition_mode='both', min_size_threshold=None):
        """
        初始化身份证图片整理器
        :param source_dir: 源图片目录
        :param output_dir: 输出目录
        :param recognition_mode: 识别模式 ('front': 只识别正面, 'back': 只识别反面, 'both': 识别正反面)
        :param min_size_threshold: 最小尺寸阈值 (width, height)，小于此尺寸的图片不进行分类
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.recognition_mode = recognition_mode

        # 设置最小尺寸阈值，默认为身份证的合理最小尺寸
        # 标准身份证尺寸约为 85.6mm × 54mm，按300DPI计算约为 1012 × 638 像素
        # 设置一个较小的阈值来过滤明显太小的图片
        self.min_size_threshold = min_size_threshold or (400, 250)  # 默认最小400x250像素

        print(f"图片尺寸过滤阈值: {self.min_size_threshold[0]}x{self.min_size_threshold[1]} 像素")

        if self.output_dir.is_file():
            raise ValueError("错误：输出路径不能是文件，请提供一个目录路径。")

        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 根据识别模式创建相应的文件夹
        if recognition_mode in ['front', 'both']:
            self.front_dir = self.output_dir / "正面"
            self.front_dir.mkdir(exist_ok=True)

        if recognition_mode in ['back', 'both']:
            self.back_dir = self.output_dir / "反面"
            self.back_dir.mkdir(exist_ok=True)

        self.unknown_dir = self.output_dir / "未识别"
        self.unknown_dir.mkdir(exist_ok=True)

        # 创建尺寸过小的文件夹
        self.too_small_dir = self.output_dir / "尺寸过小"
        self.too_small_dir.mkdir(exist_ok=True)

        # 初始化CnOcr引擎，并使用中文模型
        self.ocr = CnOcr(det_model_name='ch_PP-OCRv3_det', rec_model_name='ch_PP-OCRv3')

        self.img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']

        # 身份证正面关键词（按重要性分类）
        # 重要关键词 (+3分)
        self.front_keywords_high = [
            '中华人民共和国', '居民身份证', '公民身份号码'
        ]

        # 一般关键词 (+2分)
        self.front_keywords_medium = [
            '姓名', '性别', '民族', '出生', '住址'
        ]

        # 其他关键词 (+1分)
        self.front_keywords_low = [
            '汉族', '满族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族', '朝鲜族',
            '男', '女', '年', '月', '日', '省', '市', '县', '区', '镇', '街道', '号'
        ]

        # 身份证反面关键词（按重要性分类）
        # 重要关键词 (+3分)
        self.back_keywords_high = [
            '签发机关', '有效期限', '公安局'
        ]

        # 一般关键词 (+2分)
        self.back_keywords_medium = [
            '派出所', '有效期', '长期', '签发日期'
        ]

        # 其他关键词 (+1分)
        self.back_keywords_low = [
            # 机关相关
            '发证机关', '分局', '公安', '警察', '治安', '户政',
            # 有效期相关
            '永久', '至',
            # 日期相关
            '发证日期', '年', '月', '日',
            # 常见机关后缀
            '厅', '局', '处', '科', '所', '队',
            # 地区公安机关
            '市公安局', '县公安局', '区公安局', '公安分局',
            # 其他标识
            '中华人民共和国公安部', '监制'
        ]

        # 为了兼容性，保留原有的关键词列表
        self.front_keywords = self.front_keywords_high + self.front_keywords_medium + self.front_keywords_low
        self.back_keywords = self.back_keywords_high + self.back_keywords_medium + self.back_keywords_low
        
    def _preprocess_image(self, img_np):
        """对图像进行增强预处理，提高OCR识别准确率"""
        if img_np is None:
            return None

        # 1. 转换为灰度图
        gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)

        # 2. 图像去噪 - 使用双边滤波保持边缘
        denoised = cv2.bilateralFilter(gray, 9, 75, 75)

        # 3. 增强对比度
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)

        # 4. 锐化处理
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)

        return sharpened

    def is_image_file(self, file_path):
        """判断文件是否为图片"""
        return file_path.suffix.lower() in self.img_extensions

    def detect_id_card_region(self, img_np):
        """
        检测图片中身份证的区域大小
        :param img_np: 图片numpy数组
        :return: (is_large_enough, card_area, total_area, ratio)
        """
        if img_np is None:
            return False, 0, 0, 0.0

        height, width = img_np.shape[:2]
        total_area = width * height

        try:
            # 方法1: 基于轮廓检测
            card_area_1, card_width_1, card_height_1 = self._detect_by_contours(img_np)

            # 方法2: 基于文字区域检测（更适合身份证）
            card_area_2, card_width_2, card_height_2 = self._detect_by_text_regions(img_np)

            # 方法3: 基于颜色和纹理特征
            card_area_3, card_width_3, card_height_3 = self._detect_by_features(img_np)

            # 选择最佳检测结果
            candidates = [
                (card_area_1, card_width_1, card_height_1, "轮廓检测"),
                (card_area_2, card_width_2, card_height_2, "文字区域检测"),
                (card_area_3, card_width_3, card_height_3, "特征检测")
            ]

            # 过滤无效结果并选择最大的
            valid_candidates = [(area, w, h, method) for area, w, h, method in candidates if area > 0]

            if valid_candidates:
                best_area, best_width, best_height, best_method = max(valid_candidates, key=lambda x: x[0])

                area_ratio = best_area / total_area
                min_width, min_height = self.min_size_threshold

                # 检查身份证区域是否足够大
                size_ok = best_width >= min_width and best_height >= min_height

                print(f"最佳检测方法: {best_method}")
                print(f"检测到身份证区域: {best_width}x{best_height} 像素")
                print(f"身份证区域面积: {best_area:.0f} 像素² ({area_ratio:.1%})")
                print(f"尺寸阈值: {min_width}x{min_height}, 结果: {'✓' if size_ok else '✗'}")

                return size_ok, best_area, total_area, area_ratio
            else:
                print("所有检测方法都未找到身份证区域，使用整图尺寸")
                return self.check_image_size_fallback(img_np), total_area, total_area, 1.0

        except Exception as e:
            print(f"身份证区域检测出错: {e}")
            # 如果检测失败，回退到原来的整图尺寸检查
            return self.check_image_size_fallback(img_np), total_area, total_area, 1.0

    def check_image_size_fallback(self, img_np):
        """
        回退方案：检查整个图片尺寸
        :param img_np: 图片numpy数组
        :return: True表示尺寸足够大，False表示尺寸过小
        """
        if img_np is None:
            return False

        height, width = img_np.shape[:2]
        min_width, min_height = self.min_size_threshold

        # 检查宽度和高度是否都满足最小要求
        size_ok = width >= min_width and height >= min_height

        print(f"回退检查 - 图片尺寸: {width}x{height}, 阈值: {min_width}x{min_height}, 结果: {'✓' if size_ok else '✗'}")

        return size_ok

    def identify_id_card_side(self, ocr_results):
        """
        根据OCR结果识别身份证正反面（增强版）
        :param ocr_results: OCR识别结果列表
        :return: 'front' (正面), 'back' (反面), 'unknown' (未识别)
        """
        if not ocr_results:
            return 'unknown'

        # 提取所有识别到的文字
        all_text = ' '.join([result.get('text', '') for result in ocr_results])

        # 打印识别到的文字用于调试
        print(f"识别到的文字: {all_text}")

        front_score = 0
        back_score = 0

        # 记录匹配的关键词
        matched_front_keywords = []
        matched_back_keywords = []

        # 检查正面关键词（使用新的分类评分系统）
        # 重要关键词 (+3分)
        for keyword in self.front_keywords_high:
            if keyword in all_text:
                front_score += 3
                matched_front_keywords.append(f"{keyword}(+3)")

        # 一般关键词 (+2分)
        for keyword in self.front_keywords_medium:
            if keyword in all_text:
                front_score += 2
                matched_front_keywords.append(f"{keyword}(+2)")

        # 其他关键词 (+1分)
        for keyword in self.front_keywords_low:
            if keyword in all_text:
                front_score += 1
                matched_front_keywords.append(f"{keyword}(+1)")

        # 检查反面关键词（使用新的分类评分系统）
        # 重要关键词 (+3分)
        for keyword in self.back_keywords_high:
            if keyword in all_text:
                back_score += 3
                matched_back_keywords.append(f"{keyword}(+3)")

        # 一般关键词 (+2分)
        for keyword in self.back_keywords_medium:
            if keyword in all_text:
                back_score += 2
                matched_back_keywords.append(f"{keyword}(+2)")

        # 其他关键词 (+1分)
        for keyword in self.back_keywords_low:
            if keyword in all_text:
                back_score += 1
                matched_back_keywords.append(f"{keyword}(+1)")

        # 特殊模式识别：检查18位身份证号码
        import re
        id_pattern = r'\b\d{17}[\dXx]\b'
        if re.search(id_pattern, all_text):
            front_score += 4
            matched_front_keywords.append("18位身份证号(+4)")

        # 特殊模式识别：检查日期格式（反面常见）
        date_pattern = r'\d{4}\.\d{1,2}\.\d{1,2}'
        if re.search(date_pattern, all_text):
            back_score += 2
            matched_back_keywords.append("日期格式(+2)")

        # 打印匹配信息用于调试
        if matched_front_keywords:
            print(f"匹配的正面关键词: {', '.join(matched_front_keywords)} (总分: {front_score})")
        if matched_back_keywords:
            print(f"匹配的反面关键词: {', '.join(matched_back_keywords)} (总分: {back_score})")

        # 根据识别模式进行判断（优化后的阈值）
        if self.recognition_mode == 'front':
            # 只识别正面模式
            if front_score >= 4:  # 提高阈值，要求更严格
                return 'front'
            else:
                return 'unknown'
        elif self.recognition_mode == 'back':
            # 只识别反面模式
            if back_score >= 4:  # 提高阈值，要求更严格
                return 'back'
            else:
                return 'unknown'
        else:
            # 识别正反面模式（默认）
            # 要求明显的分数差距和足够高的分数
            score_diff = abs(front_score - back_score)

            if front_score > back_score and front_score >= 5 and score_diff >= 2:
                return 'front'
            elif back_score > front_score and back_score >= 5 and score_diff >= 2:
                return 'back'
            elif front_score == back_score and front_score >= 6:
                # 分数相同且都很高时，优先判断为正面
                return 'front'
            else:
                return 'unknown'
    
    def process_single_image(self, img_path):
        """
        处理单个图片文件，识别身份证正反面并分类
        :param img_path: 图片路径
        :return: 识别结果和分类结果
        """
        img_path = Path(img_path)
        if not img_path.is_file():
            print(f"错误：文件不存在: {img_path}")
            return None

        if not self.is_image_file(img_path):
            print(f"错误：不是支持的图片格式: {img_path}")
            return None

        print(f"正在处理图片: {img_path.name}")

        try:
            # 预先读取图片为numpy数组
            img_np = self.imread_with_unicode(str(img_path))
            if img_np is None:
                print(f"错误：无法读取图片 {img_path.name}")
                return None

            print(f"图片尺寸: {img_np.shape}")

            # 检测身份证区域大小
            is_large_enough, card_area, total_area, area_ratio = self.detect_id_card_region(img_np)

            if not is_large_enough:
                print(f"身份证区域过小，跳过OCR识别")

                # 将小尺寸图片移动到"尺寸过小"文件夹
                target_path = self.too_small_dir / img_path.name
                if target_path.exists():
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    target_path = self.too_small_dir / f"{timestamp}_{img_path.name}"

                shutil.copy2(img_path, target_path)
                print(f"图片已移动到: {target_path}")

                return {"side": "too_small", "texts": [], "target_path": str(target_path), "key_info": {}, "card_area_ratio": area_ratio}

            # 对图像进行预处理
            processed_img_np = self._preprocess_image(img_np)

            # 将预处理后的图片数据传递给CnOcr
            result = self.ocr.ocr(processed_img_np)

            if result:
                # --- 结果后处理 ---
                filtered_results = []
                confidence_threshold = 0.5  # 降低置信度阈值以获取更多文字
                min_text_length = 1         # 降低最小文本长度

                for line in result:
                    confidence = line.get('score', 0.0)
                    text = "".join(line['text']).strip()

                    # 应用过滤规则
                    if confidence >= confidence_threshold and len(text) >= min_text_length:
                        filtered_results.append({
                            "text": text,
                            "confidence": float(confidence),
                            "position": line['position']
                        })

                if not filtered_results:
                    print(f"未在图片中识别到有效文字: {img_path.name}")
                    # 移动到未识别文件夹
                    shutil.copy2(img_path, self.unknown_dir / img_path.name)
                    print(f"图片已移动到未识别文件夹")
                    return None

                # 识别身份证正反面
                side = self.identify_id_card_side(filtered_results)

                # 根据识别结果选择目标文件夹
                if side == 'front' and hasattr(self, 'front_dir'):
                    target_dir = self.front_dir
                    side_name = "正面"
                elif side == 'back' and hasattr(self, 'back_dir'):
                    target_dir = self.back_dir
                    side_name = "反面"
                else:
                    target_dir = self.unknown_dir
                    side_name = "未识别"

                # 复制图片到对应文件夹
                target_path = target_dir / img_path.name
                # 如果文件名已存在，添加时间戳
                if target_path.exists():
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    target_path = target_dir / f"{timestamp}_{img_path.name}"

                shutil.copy2(img_path, target_path)

                print(f"识别为身份证{side_name}，已移动到: {target_path}")

                # 提取关键信息
                if side in ['front', 'back']:
                    key_info = self.extract_key_info(filtered_results, side)
                    if key_info:
                        print("提取的关键信息:")
                        for key, value in key_info.items():
                            print(f"- {key}: {value}")

                # 显示识别到的关键文字
                text_results = [{"text": r["text"], "confidence": r["confidence"]} for r in filtered_results]
                print("识别到的文字:")
                for item in text_results:
                    print(f"- {item['text']} (置信度: {item['confidence']:.4f})")

                # 保存详细信息到JSON文件
                if side in ['front', 'back']:
                    json_path = target_path.with_suffix('.json')
                    detail_info = {
                        "文件名": img_path.name,
                        "识别类型": side_name,
                        "关键信息": key_info if side in ['front', 'back'] else {},
                        "OCR结果": text_results,
                        "处理时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(detail_info, f, ensure_ascii=False, indent=2)
                    print(f"详细信息已保存到: {json_path}")

                return {"side": side, "texts": text_results, "target_path": str(target_path), "key_info": key_info if side in ['front', 'back'] else {}}

            else:
                print(f"未在图片中识别到文字: {img_path.name}")
                # 移动到未识别文件夹
                shutil.copy2(img_path, self.unknown_dir / img_path.name)
                print(f"图片已移动到未识别文件夹")
                return None

        except Exception as e:
            print(f"处理图片 {img_path.name} 时出错: {str(e)}")
            return None
        
    def process_images(self):
        """批量处理所有图片文件，识别身份证正反面并分类"""
        image_files = []
        for file_path in self.source_dir.glob('**/*'):
            if file_path.is_file() and self.is_image_file(file_path):
                image_files.append(file_path)

        print(f"找到 {len(image_files)} 个图片文件")

        # 统计结果
        front_count = 0
        back_count = 0
        unknown_count = 0
        too_small_count = 0

        for idx, img_path in enumerate(image_files):
            print(f"\n处理图片 {idx+1}/{len(image_files)}: {img_path.name}")

            try:
                # 预先读取图片为numpy数组
                img_np = self.imread_with_unicode(str(img_path))
                if img_np is None:
                    print(f"  错误: 无法读取图片 {img_path.name}, 已跳过。")
                    shutil.copy2(img_path, self.unknown_dir / img_path.name)
                    unknown_count += 1
                    continue

                # 检测身份证区域大小
                is_large_enough, _, _, area_ratio = self.detect_id_card_region(img_np)

                if not is_large_enough:
                    print(f"  身份证区域过小，跳过OCR识别")

                    # 将小尺寸图片移动到"尺寸过小"文件夹
                    target_path = self.too_small_dir / img_path.name
                    if target_path.exists():
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        target_path = self.too_small_dir / f"{timestamp}_{img_path.name}"

                    shutil.copy2(img_path, target_path)
                    print(f"  已移动到: {target_path.name}")
                    too_small_count += 1
                    continue

                # 对图像进行预处理
                processed_img_np = self._preprocess_image(img_np)

                result = self.ocr.ocr(processed_img_np)

                if result:
                    # --- 结果后处理 ---
                    filtered_results = []
                    confidence_threshold = 0.5  # 降低置信度阈值
                    min_text_length = 1

                    for line in result:
                        confidence = line.get('score', 0.0)
                        text = "".join(line['text']).strip()

                        if confidence >= confidence_threshold and len(text) >= min_text_length:
                            filtered_results.append({
                                "text": text,
                                "confidence": float(confidence),
                                "position": line['position']
                            })

                    if not filtered_results:
                        print(f"  未在图片中识别到有效文字: {img_path.name}")
                        shutil.copy2(img_path, self.unknown_dir / img_path.name)
                        unknown_count += 1
                        continue

                    # 识别身份证正反面
                    side = self.identify_id_card_side(filtered_results)

                    # 根据识别结果选择目标文件夹
                    if side == 'front' and hasattr(self, 'front_dir'):
                        target_dir = self.front_dir
                        side_name = "正面"
                        front_count += 1
                    elif side == 'back' and hasattr(self, 'back_dir'):
                        target_dir = self.back_dir
                        side_name = "反面"
                        back_count += 1
                    else:
                        target_dir = self.unknown_dir
                        side_name = "未识别"
                        unknown_count += 1

                    # 复制图片到对应文件夹
                    target_path = target_dir / img_path.name
                    # 如果文件名已存在，添加时间戳
                    if target_path.exists():
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        target_path = target_dir / f"{timestamp}_{img_path.name}"

                    shutil.copy2(img_path, target_path)
                    print(f"  识别为身份证{side_name}，已移动到: {target_path.name}")

                    # 提取并保存关键信息
                    if side in ['front', 'back']:
                        key_info = self.extract_key_info(filtered_results, side)
                        if key_info:
                            print(f"  提取的关键信息: {', '.join([f'{k}:{v}' for k, v in key_info.items()])}")

                            # 保存详细信息到JSON文件
                            json_path = target_path.with_suffix('.json')
                            text_results = [{"text": r["text"], "confidence": r["confidence"]} for r in filtered_results]
                            detail_info = {
                                "文件名": img_path.name,
                                "识别类型": side_name,
                                "关键信息": key_info,
                                "OCR结果": text_results,
                                "处理时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            }
                            with open(json_path, 'w', encoding='utf-8') as f:
                                json.dump(detail_info, f, ensure_ascii=False, indent=2)

                else:
                    print(f"  未在图片中识别到文字: {img_path.name}")
                    shutil.copy2(img_path, self.unknown_dir / img_path.name)
                    unknown_count += 1

            except Exception as e:
                print(f"  处理图片 {img_path.name} 时出错: {str(e)}")
                shutil.copy2(img_path, self.unknown_dir / img_path.name)
                unknown_count += 1

        print(f"\n所有图片处理完成！")
        print(f"统计结果:")
        print(f"  身份证正面: {front_count} 张")
        print(f"  身份证反面: {back_count} 张")
        print(f"  尺寸过小: {too_small_count} 张")
        print(f"  未识别: {unknown_count} 张")
        print(f"结果保存在: {self.output_dir}")
        
    def _draw_ocr_results(self, original_img_path, output_img_path, ocr_result):
        """在图片上标注OCR识别结果"""
        try:
            # 使用 imread_with_unicode 替换直接读取，以支持中文路径
            img = self.imread_with_unicode(str(original_img_path))
            if img is None:
                print(f"  警告：无法读取图片文件进行标注: {original_img_path}")
                return
                
            for line in ocr_result:
                box = np.array(line['position']).astype(int)
                text = "".join(line['text'])
                
                cv2.polylines(img, [box], True, (0, 255, 0), 2)
                
                cv2.putText(img, text, (box[0, 0], box[0, 1] - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            annotated_path = output_img_path.parent / f"annotated_{output_img_path.name}"
            cv2.imwrite(str(annotated_path), img)
            
        except Exception as e:
            print(f"  标注图片时出错: {str(e)}")

    def imread_with_unicode(self, path):
        """使用 imdecode 解决中文路径读取问题"""
        try:
            with open(path, 'rb') as f:
                img_data = f.read()
            return cv2.imdecode(np.frombuffer(img_data, np.uint8), cv2.IMREAD_COLOR)
        except Exception as e:
            print(f"  使用imdecode读取图片时出错: {e}")
            return None

    def extract_key_info(self, ocr_results, side):
        """
        从OCR结果中提取关键信息
        :param ocr_results: OCR识别结果
        :param side: 身份证正反面 ('front' 或 'back')
        :return: 提取的关键信息字典
        """
        key_info = {}
        all_text = ' '.join([result.get('text', '') for result in ocr_results])

        if side == 'front':
            # 提取正面信息
            import re

            # 提取18位身份证号码
            id_pattern = r'\b(\d{17}[\dXx])\b'
            id_match = re.search(id_pattern, all_text)
            if id_match:
                key_info['身份证号'] = id_match.group(1)

            # 提取姓名（通常在"姓名"后面）
            name_pattern = r'姓名[：:\s]*([^\s\d]{2,4})'
            name_match = re.search(name_pattern, all_text)
            if name_match:
                key_info['姓名'] = name_match.group(1)

            # 提取性别
            if '男' in all_text:
                key_info['性别'] = '男'
            elif '女' in all_text:
                key_info['性别'] = '女'

            # 提取民族
            for text in [result.get('text', '') for result in ocr_results]:
                if '族' in text and len(text) <= 4:
                    key_info['民族'] = text
                    break

            # 提取出生日期
            birth_pattern = r'(\d{4})年(\d{1,2})月(\d{1,2})日'
            birth_match = re.search(birth_pattern, all_text)
            if birth_match:
                key_info['出生日期'] = f"{birth_match.group(1)}-{birth_match.group(2).zfill(2)}-{birth_match.group(3).zfill(2)}"

        elif side == 'back':
            # 提取反面信息
            import re

            # 提取签发机关
            for text in [result.get('text', '') for result in ocr_results]:
                if '公安局' in text or '派出所' in text:
                    key_info['签发机关'] = text
                    break

            # 提取有效期
            date_pattern = r'(\d{4})\.(\d{1,2})\.(\d{1,2})'
            dates = re.findall(date_pattern, all_text)
            if dates:
                if len(dates) >= 2:
                    key_info['签发日期'] = f"{dates[0][0]}-{dates[0][1].zfill(2)}-{dates[0][2].zfill(2)}"
                    if '长期' in all_text:
                        key_info['有效期限'] = '长期'
                    else:
                        key_info['有效期限'] = f"{dates[1][0]}-{dates[1][1].zfill(2)}-{dates[1][2].zfill(2)}"
                elif len(dates) == 1:
                    key_info['签发日期'] = f"{dates[0][0]}-{dates[0][1].zfill(2)}-{dates[0][2].zfill(2)}"

        return key_info

def main():
    print("身份证图片整理工具 (基于 CnOcr)")
    print("功能：自动识别身份证正面和反面，并分类整理到不同文件夹")

    source_path_str = input("请输入要处理的图片文件或文件夹的完整路径: ").strip()
    source_path = Path(source_path_str)

    if not source_path.exists():
        print(f"错误：路径不存在: {source_path_str}")
        return

    output_dir = input("请输入结果保存目录路径（默认为'./id_card_output'）: ") or "./id_card_output"

    # 选择识别模式
    print("\n请选择识别模式:")
    print("1. 只识别正面")
    print("2. 只识别反面")
    print("3. 识别正面和反面（默认）")

    mode_choice = input("请输入选择（1/2/3，默认为3）: ").strip() or "3"

    mode_map = {
        "1": "front",
        "2": "back",
        "3": "both"
    }

    recognition_mode = mode_map.get(mode_choice, "both")

    mode_names = {
        "front": "只识别正面",
        "back": "只识别反面",
        "both": "识别正面和反面"
    }

    # 选择尺寸过滤设置
    print(f"\n当前识别模式: {mode_names[recognition_mode]}")
    print("\n尺寸过滤设置:")
    print("1. 使用默认尺寸阈值 (400x250 像素)")
    print("2. 自定义尺寸阈值")
    print("3. 不进行尺寸过滤")

    size_choice = input("请输入选择（1/2/3，默认为1）: ").strip() or "1"

    min_size_threshold = None
    if size_choice == "1":
        min_size_threshold = (400, 250)
        print(f"使用默认尺寸阈值: {min_size_threshold[0]}x{min_size_threshold[1]} 像素")
    elif size_choice == "2":
        try:
            width = int(input("请输入最小宽度（像素）: ").strip())
            height = int(input("请输入最小高度（像素）: ").strip())
            min_size_threshold = (width, height)
            print(f"使用自定义尺寸阈值: {min_size_threshold[0]}x{min_size_threshold[1]} 像素")
        except ValueError:
            print("输入无效，使用默认尺寸阈值")
            min_size_threshold = (400, 250)
    else:
        min_size_threshold = (0, 0)  # 不进行尺寸过滤
        print("不进行尺寸过滤，处理所有图片")

    print(f"已选择模式: {mode_names[recognition_mode]}")

    try:
        if source_path.is_file():
            print("检测到输入为单个文件，开始处理...")
            organizer = IDCardOrganizer("", output_dir, recognition_mode, min_size_threshold)
            result = organizer.process_single_image(source_path)
            if result:
                print(f"处理完成！图片已分类到: {result['target_path']}")
        elif source_path.is_dir():
            print("检测到输入为文件夹，开始批量处理...")
            organizer = IDCardOrganizer(source_path, output_dir, recognition_mode, min_size_threshold)
            organizer.process_images()
        else:
            print(f"错误：路径既不是文件也不是文件夹: {source_path_str}")

    except ValueError as e:
        print(e)
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    main() 